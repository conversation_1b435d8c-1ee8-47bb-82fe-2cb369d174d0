# Multi-stage Docker build for wise-match-agents
FROM python:3.12-slim AS base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=300 \
    PIP_TIMEOUT=300 \
    POETRY_NO_INTERACTION=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# Install system dependencies
RUN sed -i \
    -e 's|http://deb.debian.org|http://mirrors.aliyun.com|g' \
    -e 's|http://security.debian.org/debian-security|http://mirrors.aliyun.com/debian-security|g' \
    /etc/apt/sources.list.d/debian.sources && \
    apt-get update --allow-unauthenticated && \
    apt-get install -y --allow-unauthenticated \
    build-essential \
    curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Configure pip to use <PERSON><PERSON> mirror for faster downloads in China
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host mirrors.aliyun.com

# Install Poetry with retries and extended timeout
RUN pip install --timeout=300 --retries=5 poetry==2.2.0

# Set work directory
WORKDIR /app

# Copy poetry files
COPY pyproject.toml poetry.lock* ./

# Configure poetry to use Aliyun mirror and install dependencies
RUN poetry config virtualenvs.create false && \
    poetry source add --priority=primary aliyun https://mirrors.aliyun.com/pypi/simple/ && \
    poetry lock && \
    poetry install --only=main --no-root && \
    rm -rf $POETRY_CACHE_DIR

# Develop stage
FROM base as develop

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Copy application code
COPY --chown=appuser:appuser . .

# Create necessary directories
RUN mkdir -p logs && \
    chown -R appuser:appuser logs

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Production command
CMD ["python", "-m", "wise_match_agents.run_service", "--host", "0.0.0.0", "--port", "8000", "--env", "prod"]