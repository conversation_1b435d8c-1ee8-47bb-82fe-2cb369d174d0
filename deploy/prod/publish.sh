#!/bin/bash
set -eu

# 构建最新镜像
docker build -f deploy/dev/Dockerfile -t wise-match-agents:prod --no-cache .

# 登录远程镜像仓库（只需要执行1次就行）
# 镜像仓库地址：https://cr.console.aliyun.com/cn-hangzhou/instance/repositories
# $ docker login --username=puteedu2024 crpi-o6u11ymyucz0k15d.cn-hangzhou.personal.cr.aliyuncs.com Wisematch@888

# 发布最新镜像
docker tag wise-match-agents:prod crpi-o6u11ymyucz0k15d.cn-hangzhou.personal.cr.aliyuncs.com/wisematch/wise-match-agents:prod
docker push crpi-o6u11ymyucz0k15d.cn-hangzhou.personal.cr.aliyuncs.com/wisematch/wise-match-agents:prod

# 清理无 tag 的镜像
if docker images | grep -q none; then
  docker images | grep none | awk '{print $3}' | xargs docker image rm -f
fi
